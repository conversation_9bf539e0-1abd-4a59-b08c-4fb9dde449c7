<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>开发者你好，欢迎来到HMOS代码工坊</title>
  <link rel="stylesheet" href="../common/css/banner.css">
  <link rel="stylesheet" href="./index.css" />
  <link rel="stylesheet" href="../common/css/multi.css">
  <link rel="preload" as="image" href="./image/banner.png">
  <link rel="preload" as="image" href="./image/bg_1.png">
  <link rel="preload" as="image" href="./image/bg_2.png">
  <link rel="preload" as="image" href="./image/bg_3.png">
</head>

<body>
  <div class="main-content">
    <!-- header -->
    <div class="header">
      <div class="title">开发者你好，欢迎来到HMOS代码工坊</div>
      <div class="content image-cover">
        <img class="banner-img" src="./image/banner.png" alt="">
        <div class="image-words">
          <div>欢迎来到HarmonyOS代码工坊</div>
          <div>承载应用开发最佳实践</div>
        </div>
      </div>
    </div>

    <!-- body -->
    <div class="chapter">
      <div class="section">
        <div class="bg-container">
          <div class="title-container">
            <div class="main-title" style="color-scheme: light dark;">开箱即用</div>
            <div class="sub-title" style="color-scheme: light dark;">UI组件和系统能力</div>
          </div>
          <div class="bottom-text" style="color-scheme: light dark;">
            <div>能实时调整属性、样式、代码</div>
            <div>并直接在手机上体验最终效果</div>
          </div>
          <img class="phone-gif"
            src="./image/video_comp.jpeg"
            alt="" />
          <img class="phone-border" draggable="false"
            src="./image/phone.png"
            alt="" />
        </div>
        <img class="bg-img" src="./image/bg_1.png" alt="">
      </div>
      <div class="section">
        <div class="bg-container">
          <div class="title-container">
            <div class="main-title" style="color-scheme: light dark;">开发案例</div>
            <div class="sub-title" style="color-scheme: light dark;">无需编译 立即体验</div>
          </div>
          <div class="bottom-text" style="color-scheme: light dark;">
            <div>通过动态加载技术，直接在HarmonyOS</div>
            <div>代码工坊中体验Samples案例</div>
          </div>
          <img class="phone-gif"
            src="./image/video_devp.jpeg"
            alt="" />
          <img class="phone-border" draggable="false"
            src="./image/phone.png"
            alt="" />
        </div>
        <img class="bg-img" src="./image/bg_2.png" alt="">
      </div>
      <div class="section">
        <div class="bg-container">
          <div class="title-container">
            <div class="main-title" style="color-scheme: light dark;">最佳实践</div>
            <div class="sub-title" style="color-scheme: light dark;">鸿蒙最新特性抢先看</div>
          </div>
          <div class="bottom-text" style="color-scheme: light dark;">
            <div class="text-safety">实时传递HarmonyOS最新亮点特性助力开发者更高效、更便捷地开发出体验更佳的鸿蒙应用</div>
          </div>
          <img class="phone-gif"
            src="./image/article.jpeg"
            alt="" />
          <img class="phone-border" draggable="false"
            src="./image/phone.png"
            alt="" />
        </div>
        <img class="bg-img" src="./image/bg_3.png" alt="">
      </div>
    </div>
  </div>

  <!-- footer -->
  <div class="footer">
    <img class="footerImg"
      src="../common/image/f_icon.png"
      alt="" />
  </div>
</body>
<script src="../common/js/banner.js"></script>

</html>