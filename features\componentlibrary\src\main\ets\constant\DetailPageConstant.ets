/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export class DetailPageConstant {
  public static GRID_POPUP_OFFSET_Y: number = -10;
  public static IMAGE_POPUP_OFFSET_Y: number = -50;
  public static LONG_DURATION: number = 2000;
  public static ANIMATION_DURATION: number = 500;
  public static ANIMATION_DURATION_SHORT: number = 300;
  public static CUSTOM_DIALOG_CONTENT_HEIGHT: number = 100;
  public static DETAIL_SELECT_COMPONENT_HEIGHT: number = 48;
  public static WATER_FLOW_MIN_SIZE: number = 92;
  public static WATER_FLOW_MAX_SIZE: number = 180;
  public static SWIPER_BACKGROUND_COLOR: string = 'rgba(0, 85, 255, 0.1)';
  public static IMAGE_OPACITY: number = 0.4;
  public static IMAGE_OPACITY_BG: number = 0.2;
  public static PROGRESS_MAX_VALUE: number = 100;
  public static PROGRESS_CIRCLE_WIDTH: number = 64;
  public static PROGRESS_LINE_HEIGHT: number = 48;
  public static PROGRESS_CAPSULE_HEIGHT: number = 24;
  public static SCALE_LEVEL1: number = 1.0;
  public static ASPECT_RATIO_SQUARE: number = 1;
  public static CODEPREVIEW_IMMERSIVE_HEIGHT = 16;
  // AspectRatio invalid value
  public static ASPECT_RATIO_INVALID: number = -1;
  // Threshold for the number of columns
  public static LIST_LANES_THRESHOLD: number = 4;
  public static ALERT_DIALOG_OFFSET_Y: number = -20;
  public static ACTION_SHEET_OFFSET_Y: number = -10;
  public static SELECT_RESULT_DIALOG_SIZE: number = 300;
  public static MARGIN_NEGATIVE_LARGER: number = -95;
  // Space definition.
  public static SPACE_NORMAL: number = 8;
  public static SPACE_SMALL: number = 5;
  public static SPACE_LARGE: number = 20;
  // Opacity level.
  public static OPACITY_SMALL: number = 0.2;
  // toggle.
  public static TOGGLE_WIDTH: number = 120;
  public static TOGGLE_HEIGHT: number = 28;
  // column.
  public static CONTAINER_BORDER: number = 1;
  // flex.
  public static FLEX_SPACE: number = 6;
  // Page Area Size.
  public static PREVIEW_HEIGHT_SM: number = 284;
  public static PREVIEW_SUB_HEIGHT_SM: number = 230;
  public static ATTRIBUTE_ITEM_HEIGHT: number = 56;
  public static TEXT_TIP_HEIGHT: number = 50;
  // Font line height.
  public static LINE_HEIGHT_LARGE: number = 16;
  // Offset level.
  public static SCROLL_OFFSET_Y: number = 20;
  public static MENU_ITEM_HEIGHT: number = 46;
  public static TEXT_MAX_LINES: number = 3;
  // CalendarPicker hintRadius number
  public static DATE_HINT_RADIUS: number = 10;
  // Animation duration for component displacement when the keyboard appears
  public static UP_DURATION: number = 300;
  public static CALENDAR_DEFAULT_FONT_SIZE: number = 20;
  public static CALENDAR_DEFAULT_FONT_SIZE2: number = 16;
  public static COMPONENT_GAP_SIZE: number = 20;
  public static COMPONENT_GAP_SIZE2: number = 40;
  // The offset of the popup component relative to the display position defined by the placement setting.
  public static HOVER_POPUP_LEFT: number = 10;
  public static HOVER_POPUP_TOP: number = 8;
  public static PEN_CLOSE_TOP: number = 56;
}