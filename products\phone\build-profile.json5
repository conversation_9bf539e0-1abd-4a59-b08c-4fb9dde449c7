{
  "apiType": "stageMode",
  "buildOption": {
    "arkOptions": {
      "runtimeOnly": {
        "packages": [
          "@ohos/componentlibrary",
          "@ohos/devpractices",
          "@ohos/exploration",
          "@ohos/mine",
          "@ohos/commonbusiness",
          "@ohos/common",
          "@ohos/exploration"
        ]
      }
    },
  },
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": [
              "./obfuscation-rules.txt"
            ]
          }
        }
      }
    },
  ],
  "targets": [
    {
      "name": "default"
    },
    {
      "name": "ohosTest",
    }
  ]
}