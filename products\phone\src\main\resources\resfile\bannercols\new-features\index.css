/*  body  */
.bg-black {
  background-color: rgb(16, 16, 16);
}
.bg-white {
  background-color: #e0dfdf;
}

.section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 648px;
  overflow: hidden;
}

.large-content {
  height: 580px;
}

.bg-img {
  position: absolute;
  top: 0;
  height: 100%;
}

.bg-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding-top: 48px;
  background-size: auto 100%;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 99;
}

.title-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

.main-title {
  font-size: 30px;
  margin-bottom: 6px;
}

.sub-title {
  font-size: 20px;
}

.bg-black .title-container .main-title,
.bg-black .title-container .sub-title {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 1) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.bg-white .title-container .main-title {
  color: #303030;
}

.bg-white .title-container .sub-title {
  color: #5a5a5a;
}

.phone-border {
  position: absolute;
  top: 163px;
  left: 50%;
  transform: translateX(-50%);
  width: 360px;
}

.phone-gif {
  position: absolute;
  top: 171px;
  left: 50%;
  transform: translateX(-50%);
  width: 154px;
  border-radius: 16px;
}

.large-gif {
  position: absolute;
  top: 171px;
  left: 50%;
  transform: translateX(-50%) translateX(0.5px);
  width: 352px;
  border-radius: 16px;
}

.phone-double {
  position: absolute;
  top: 171px;
  left: 50%;
  transform: translateX(-50%) translateX(-4px);
  width: 179px;
  height: 340px;
}

.left-phone {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) translateX(-78px) translateY(10px);
  width: 147px;
  height: 307px;
  border-radius: 16px;
}

.left-border {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) translateX(-78px);
  width: 339px;
}

.right-phone {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) translateX(87px) translateY(10px);
  width: 147px;
  height: 307px;
  border-radius: 16px;
}

.right-border {
  position: absolute;
  left: 50%;
  transform: translateX(-50%) translateX(86px);
  width: 339px;
}

.delicate-translate {
  top: 165px;
  transform: translateX(-50%) translateX(-10px);
  width: 454px;
}

.extra-translate {
  width: 156px;
  height: 328px;
}

.safe-translate {
  top: 175px;
  width: 149px;
}

.bottom-text {
  position: absolute;
  width: 100%;
  bottom: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  font-size: 14px;
  line-height: 19px;
  font-weight: 500;
}

.bg-black .bottom-text {
  color: rgba(255, 255, 255, 0.6);
}

.bg-white .bottom-text {
  color: #5a5a5a;
}

@media (prefers-color-scheme: dark) {
  .bg-black .title-container .main-title,
  .bg-black .title-container .sub-title {
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 1) 50%,
      rgba(255, 255, 255, 0.2) 100%
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  
  .bg-white .title-container .main-title {
    color: #303030;
  }
  
  .bg-white .title-container .sub-title {
    color: #5a5a5a;
  }

  .bg-black .bottom-text {
    color: rgba(255, 255, 255, 0.6);
  }
  
  .bg-white .bottom-text {
    color: #5a5a5a;
  }
}