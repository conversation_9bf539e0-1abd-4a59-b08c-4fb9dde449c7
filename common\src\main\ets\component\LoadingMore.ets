/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License,Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@Builder
export function LoadingMore() {
  Row() {
    LoadingProgress()
      .color($r('sys.color.font_primary'))
      .size({ width: $r('sys.float.padding_level12'), height: $r('sys.float.padding_level12') })

    Text($r('app.string.loading'))
      .fontColor($r('sys.color.font_primary'))
      .fontSize($r('sys.float.Subtitle_S'))
      .margin({ left: $r('sys.float.padding_level4') })
  }
  .width('100%')
  .height($r('app.float.loading_more_height'))
  .justifyContent(FlexAlign.Center)
  .margin({ bottom: $r('sys.float.padding_level6') })
}