/*  body  */
.chapter {
  background-color: rgb(241, 243, 245);
}

.section {
  padding: 0px 16px;
}

.button {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.section-button {
  padding: 0 16px;
  height: 40px;
  border-radius: 26px;
  background-color: #0a59f7;
  display: inline-block;
  color: #f1f3f5;
  text-align: center;
  line-height: 40px;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.section-button:active {
  transform: scale(0.95);
}

.bg-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding-top: 48px;
  overflow: hidden;
}

.title-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

.main-title {
  font-size: 30px;
  margin-bottom: 6px;
  color: #303030;
}

.sub-title {
  font-size: 20px;
  color: #5a5a5a;
}

.wrap-phobe {
  margin-top: 40px;
  position: relative;
}

.phone-border {
  position: absolute;
  top: -11px;
  left: 50%;
  transform: translateX(-50%);
  width: 365px;
  pointer-events: none;
}

.phone-gif {
  margin: auto;
  width: 152px;
  border-radius: 16px;
}

.wrap-btn {
  display: flex;
  justify-content: space-between;
  margin-top: 50px;
  padding: 4px;
  width: 108px;
  height: 48px;
  border-radius: 24px;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1);
}

.wrap-btn .video-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-size: auto 100%;
  background-position: center;
  background-repeat: no-repeat;
  transition: all 0.2s ease; /* 平滑过渡 */
  cursor: pointer;
}

/* 点击时触发 :active 伪类 */
.video-btn:active {
  transform: scale(0.9); /* 点击时缩小 */
}

#play-btn {
  background-image: url(./image/play_circle_fill.png);
}

#stop-btn {
  background-image: url(./image/stop_circle_fill.png);
}

.bottom-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 50px;
  width: 100%;
  color: #5a5a5a;
  font-size: 14px;
  line-height: 19px;
  font-weight: 500;
}

.bg-black .bottom-text {
  color: rgba(255, 255, 255, 0.6);
}

.bg-white .bottom-text {
  color: rgba(0, 0, 0, 0.6);
}

@media (prefers-color-scheme: dark) {
  .wrap-btn {
    background-color: rgba(255, 255, 255, 0.8);
  }
}