/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { DetailPageConstant } from '../../../constant/DetailPageConstant';
import type { DescriptorWrapper } from '../../../viewmodel/DescriptorWrapper';
import { SwiperAttributeModifier } from '../viewmodel/SwiperAttributeModifier';
import type { SwiperDescriptor } from '../viewmodel/SwiperDescriptor';

function getSwiperData(): string[] {
  const list: string[] = ['1', '2', '3'];
  return list;
}

@Builder
export function SwiperBuilder($$: DescriptorWrapper) {
  Column() {
    Swiper() {
      ForEach(getSwiperData(), (item: string, _index: number) => {
        Text(item)
          .height($r('app.float.swiper_height'))
          .backgroundColor(DetailPageConstant.SWIPER_BACKGROUND_COLOR)
          .textAlign(TextAlign.Center)
          .fontSize($r('sys.float.Body_L'))
      }, (item: string) => item)
    }
    .width('100%')
    .height('100%')
    .loop(false)
    .attributeModifier(new SwiperAttributeModifier($$.descriptor as SwiperDescriptor))
  }
  .width('100%')
  .height('100%')
  .justifyContent(FlexAlign.Center)
  .borderRadius($r('sys.float.corner_radius_level8'))
  .clip(true)
}