/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License,Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { BannerData } from '@ohos/commonbusiness';
import { MediaTypeEnum } from '@ohos/commonbusiness';

export class DiscoverContent {
  public id: number = 0;
  public type: ArticleTypeEnum = ArticleTypeEnum.UNKNOWN;
  public mediaType: MediaTypeEnum = MediaTypeEnum.IMAGE;
  public mediaUrl: string = '';
  public title: string = '';
  public subTitle: string = '';
  public desc: string = '';
  public publishTime: string = '';
  public author: string = '';
  public detailsUrl: string = '';
  public urlData: string = '';
}

@Observed
export class DiscoverCardData {
  public id: number = 0;
  public name: string = '';
  public type: ArticleTypeEnum = ArticleTypeEnum.UNKNOWN;
  public contents: DiscoverContent[] = [];
}

export class DiscoverData {
  public bannerInfos?: BannerData[];
  public discoveryData: DiscoverCardData[] = [];
}

export enum ArticleTypeEnum {
  FEED = 1,
  EXPERIENCES = 2,
  ARCHITECTURE = 3,
  DEVELOPER = 4,
  UNKNOWN = 0,
}