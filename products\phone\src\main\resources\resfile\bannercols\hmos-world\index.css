/*  body  */
.section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 648px;
  overflow: hidden;
}

.bg-img {
  position: absolute;
  top: 0;
  height: 100%;
}

.bg-container {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding-top: 48px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 99;
}

.title-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}

.main-title {
  font-size: 30px;
  margin-bottom: 12px;
  color: #000000e6;
}

.sub-title {
  font-size: 20px;
  color: #00000099;
  max-width: 264px;
  text-align: center;
}

.phone-border {
  position: absolute;
  top: 171px;
  left: 50%;
  transform: translateX(-50%);
  width: 360px;
}

.phone-gif {
  position: absolute;
  top: 182px;
  left: 50%;
  transform: translateX(-50%);
  width: 151px;
  border-radius: 18px;
}

.bottom-text {
  position: absolute;
  width: 100%;
  bottom: 46px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #00000099;
  font-size: 14px;
  line-height: 19px;
}

.text-safety {
  width: 295px;
  text-align: center;
}

.button-wrap {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 0 24px;
}

@media (prefers-color-scheme: dark) {
  .main-title {
    color: #000000e6;
  }

  .sub-title, .bottom-text {
    color: #00000099;
  }
}