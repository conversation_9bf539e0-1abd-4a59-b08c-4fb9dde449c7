<!DOCTYPE html>
<html lang="zh-cn" xml:lang="zh-cn">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0, user-scalable=0" />
    <title>UX动效设计</title>
    <link rel="stylesheet" href="../../common/dist/main.css">
    <link rel="stylesheet" href="../../common/css/article.css">
  </head>

  <body>
    <div class="nested0" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002247873541"><a name="ZH-CN_TOPIC_0000002247873541"></a><a name="ZH-CN_TOPIC_0000002247873541"></a><h1 class="topictitle1"><span class="topictitlenumber1">1</span>   UX动效设计</h1>
    <div class="topicbody" id="body0000002160379872"></div>
    <div>
    <ul class="ullinks">
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002212713648">1.1  UX动效设计</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002247793453">1.2  转场动效</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002212873424">1.3  手势动效</a></strong><br>
    </li>
    </ul>
    </div><div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002212713648"><a name="ZH-CN_TOPIC_0000002212713648"></a><a name="ZH-CN_TOPIC_0000002212713648"></a><h2 class="topictitle2"><span class="topictitlenumber2">1.1</span>   UX动效设计</h2>
    <p id="ZH-CN_TOPIC_0000002212713648__p115mcpsimp"><strong id="ZH-CN_TOPIC_0000002212713648__b116mcpsimp">人因x动效</strong></p>
    <p style="color:#ee8d3d;" id="ZH-CN_TOPIC_0000002212713648__p117mcpsimp"><strong id="ZH-CN_TOPIC_0000002212713648__b113010100292">力的既视感</strong></p>
    <ul id="ZH-CN_TOPIC_0000002212713648__ul118mcpsimp"><li id="ZH-CN_TOPIC_0000002212713648__li119mcpsimp">将力赋予元素</li><li id="ZH-CN_TOPIC_0000002212713648__li120mcpsimp">直观地传递出形象化、拟物化、动态化</li></ul>
    <div class="fignone" id="ZH-CN_TOPIC_0000002212713648__fig17325538113410"><span class="figcap"><span class="figurenumber">图1-2</span>  </span><br><img id="ZH-CN_TOPIC_0000002212713648__image2326638183419" src="ManulImages/2.gif"></div>
    <p style="color:#EE8D3D;" id="ZH-CN_TOPIC_0000002212713648__p122mcpsimp"><strong id="ZH-CN_TOPIC_0000002212713648__b1894616151298">力的秩序感</strong></p>
    <ul id="ZH-CN_TOPIC_0000002212713648__ul123mcpsimp"><li id="ZH-CN_TOPIC_0000002212713648__li124mcpsimp">有序的、一致的、节奏的运动能够创造更强的秩序感</li><li id="ZH-CN_TOPIC_0000002212713648__li125mcpsimp">秩序感更容易引起用户注意力</li><li id="ZH-CN_TOPIC_0000002212713648__li126mcpsimp">秩序感对转场元素的运动编排尤为重要</li></ul>
    <div class="fignone" id="ZH-CN_TOPIC_0000002212713648__fig10331729173520"><span class="figcap"><span class="figurenumber">图1-3</span>  </span><br><img id="ZH-CN_TOPIC_0000002212713648__image133392911352" src="ManulImages/3.gif"></div>
    <p style="color:#EE8D3D;" id="ZH-CN_TOPIC_0000002212713648__p128mcpsimp"><strong id="ZH-CN_TOPIC_0000002212713648__b136341319192916">力的控制感</strong></p>
    <ul id="ZH-CN_TOPIC_0000002212713648__ul129mcpsimp"><li id="ZH-CN_TOPIC_0000002212713648__li130mcpsimp">摩擦力、重力、斥力在交互界面中以操作的方式被感知</li><li id="ZH-CN_TOPIC_0000002212713648__li131mcpsimp">力的传递与心理预期产生呼应</li><li id="ZH-CN_TOPIC_0000002212713648__li132mcpsimp">元素通过速度或节奏等，传达出仿真物理世界的感受</li></ul>
    <div class="fignone" id="ZH-CN_TOPIC_0000002212713648__fig175584237362"><span class="figcap"><span class="figurenumber">图1-4</span>  </span><br><img id="ZH-CN_TOPIC_0000002212713648__image19558162318367" src="ManulImages/4.gif"></div>
    <p style="color:#EE8D3D;" id="ZH-CN_TOPIC_0000002212713648__p134mcpsimp"><strong id="ZH-CN_TOPIC_0000002212713648__b846672232914">自然流畅</strong></p>
    <ul id="ZH-CN_TOPIC_0000002212713648__ul135mcpsimp"><li id="ZH-CN_TOPIC_0000002212713648__li136mcpsimp">流畅性是人对加工信息难易程度的一种主观体验</li><li id="ZH-CN_TOPIC_0000002212713648__li137mcpsimp">三者合一：手势触控、视觉感知、心理预期</li><li id="ZH-CN_TOPIC_0000002212713648__li138mcpsimp">基础要求：帧率稳定、响应及时、跟手操作</li></ul>
    <div class="fignone" id="ZH-CN_TOPIC_0000002212713648__fig109471141113620"><span class="figcap"><span class="figurenumber">图1-5</span>  </span><br><img id="ZH-CN_TOPIC_0000002212713648__image3947941153611" src="ManulImages/5.gif"></div>
    <p style="color:#EE8D3D;" id="ZH-CN_TOPIC_0000002212713648__p140mcpsimp"><strong id="ZH-CN_TOPIC_0000002212713648__b3394172572917">高效快速</strong></p>
    <ul id="ZH-CN_TOPIC_0000002212713648__ul141mcpsimp"><li id="ZH-CN_TOPIC_0000002212713648__li142mcpsimp">界面元素运动尽可能少且简洁</li><li id="ZH-CN_TOPIC_0000002212713648__li143mcpsimp">长距离保持在350ms内</li><li id="ZH-CN_TOPIC_0000002212713648__li144mcpsimp">短距离保持在250ms内</li><li id="ZH-CN_TOPIC_0000002212713648__li145mcpsimp">较短的动画保持在150ms内完成动作</li></ul>
    <div class="fignone" id="ZH-CN_TOPIC_0000002212713648__fig137401658103615"><span class="figcap"><span class="figurenumber">图1-6</span>  </span><br><img id="ZH-CN_TOPIC_0000002212713648__image7740558183611" src="ManulImages/6.gif"></div>
    <p style="color:#EE8D3D;" id="ZH-CN_TOPIC_0000002212713648__p147mcpsimp"><strong id="ZH-CN_TOPIC_0000002212713648__b1736982912915">运动一致</strong></p>
    <ul id="ZH-CN_TOPIC_0000002212713648__ul148mcpsimp"><li id="ZH-CN_TOPIC_0000002212713648__li149mcpsimp">在动作编排时应遵循运动路径方式一致</li><li id="ZH-CN_TOPIC_0000002212713648__li150mcpsimp">不一致的运动会分散用户视觉焦点</li><li id="ZH-CN_TOPIC_0000002212713648__li151mcpsimp">运用适当的编排手法，引导用户聚焦操作任务</li></ul>
    <div class="fignone" id="ZH-CN_TOPIC_0000002212713648__fig45761324183716"><span class="figcap"><span class="figurenumber">图1-7</span>  </span><br><img id="ZH-CN_TOPIC_0000002212713648__image1157614244373" src="ManulImages/7.gif"></div>
    </div>
    <div></div></div>
    <div class="note" id="ZH-CN_TOPIC_0000002169324749__note1352816562915"><img src=""><span class="notetitle">
    </span>
    <div class="notebody">
      <p id="ZH-CN_TOPIC_0000002169324749__p121834932912">更多UX动效设计请参考：<a
          href="article_uxanimation_1"
          target="_blank" rel="noopener noreferrer">UX设计指南-动效</a>。</p>
    </div>
  </div>
    </div>
  </body>

  <script type="module" src="../../common/js/changehref.js"></script>
  <script type="module" src="../../common/dist/main.js"></script>
</html>
