<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>鸿蒙应用开发快速入门</title>
  <link rel="stylesheet" href="../common/css/banner.css">
  <link rel="stylesheet" href="./index.css" />
  <link rel="stylesheet" href="../common/css/multi.css">
  <link rel="preload" as="image" href="./image/banner.png">
</head>

<body>
  <div class="main-content">
    <div class="header">
      <div class="title">鸿蒙应用开发快速入门</div>
      <div class="sub-title">HarmonyOS应用开发快速入门</div>
      <div class="content">
        <img class="banner-img" src="./image/banner.png" alt="">
        <div class="banner-btn jump-link"
          href="article_quickstart_1">前往体验快速入门案例
        </div>
      </div>
    </div>
    <div class="section">
      <div class="text">
        HarmonyOS
        ArkUI提供了丰富多样的UI组件，您可以使用这些组件轻松地编写出更加丰富漂亮的界面。
      </div>

      <div class="divider" style="color-scheme: light dark;"></div>

      <div class="chapter">
        <h3>使用常见组件构建页面</h3>
        <div class="text">
          您将学习使用Text、Image等常用组件、Swiper、Grid、List、Scroll等可滑动组件，构造出第一个HarmonyOS页面。
        </div>
        <div class="list-card">
          <!-- 开发入门：Hello World -->
          <div class="list-card-item jump-link"
            href="article_quickstart_2">
            <span class="list-title">开发入门：Hello World</span>
            <span class="time-box">15分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_3">
            <span class="list-title">使用Swiper构建运营推荐位</span>
            <span class="time-box">20分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_4">
            <span class="list-title">创建Item视图</span>
            <span class="time-box">20分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_5">
            <span class="list-title">网格和列表组件的使用</span>
            <span class="time-box">20分钟</span>
          </div>
        </div>
      </div>

      <div class="divider" style="color-scheme: light dark;"></div>

      <div class="chapter">
        <h3>应用架构设计基础</h3>
        <div class="text">
          通过架构设计，使您的应用更易于维护、扩展和测试。采用MVVM模式和分层架构设计，改造您的应用。
        </div>
        <div class="list-card">
          <div class="list-card-item jump-link"
            href="article_quickstart_6">
            <span class="list-title"> 应用架构设计基础——MVVM模式 </span>
            <span class="time-box">20分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_7">
            <span class="list-title"> 应用架构设计基础——三层架构 </span>
            <span class="time-box">20分钟</span>
          </div>
        </div>
      </div>

      <div class="divider" style="color-scheme: light dark;"></div>

      <div class="chapter">
        <h3>构建更丰富的界面</h3>
        <div class="text">
          通过对Web界面的支持、以及更加丰富的数据结构与组件导航。实现更加丰富的界面显示。
        </div>
        <div class="list-card">
          <div class="list-card-item jump-link"
            href="article_quickstart_8">
            <span class="list-title"> ArkWeb页面适配 </span>
            <span class="time-box">15分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_9">
            <span class="list-title"> 通过结构化数据构建页面 </span>
            <span class="time-box">25分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_10">
            <span class="list-title"> 设置组件导航 </span>
            <span class="time-box">25分钟</span>
          </div>
        </div>
      </div>

      <div class="divider" style="color-scheme: light dark;"></div>

      <div class="chapter">
        <h3>HarmonyOS特性介绍</h3>
        <div class="text">
          基于HarmonyOS中的AI语音朗读让您的应用支持界面内的文本语音朗读，通过一次开发，多端部署的能力让您的应用可以在多个设备上运行，通过分布式流转能力让您的应用可以在您的各个设备之间进行无缝流转。
        </div>
        <div class="list-card">
          <div class="list-card-item jump-link"
            href="article_quickstart_11">
            <span class="list-title"> 原生智能：AI语音朗读 </span>
            <span class="time-box">25分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_12">
            <span class="list-title"> 一次开发，多端部署 </span>
            <span class="time-box">25分钟</span>
          </div>
          <div class="list-card-item jump-link"
            href="article_quickstart_13">
            <span class="list-title"> 原生互联：分布式流转 </span>
            <span class="time-box">25分钟</span>
          </div>
        </div>
      </div>
    </div>

    <!-- footer -->
    <div class="footer">
      <img class="footerImg"
        src="../common/image/f_icon.png"
        alt="" />
    </div>
  </div>
  <script type="module" src="./index.js"></script>
</body>
<script src="../common/js/banner.js"></script>

</html>