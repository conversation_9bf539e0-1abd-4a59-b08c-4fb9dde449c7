/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { cameraPicker } from '@kit.CameraKit';
import { CommonDescriptor } from '../../../../viewmodel/CommonDescriptor';
import { pickerMediaType } from '../entity/CameraPickerMapping';
import type { OriginAttribute } from '../../../../viewmodel/Attribute';

@Observed
export class CameraPickerDescriptor extends CommonDescriptor {
  public mediaTypes: cameraPicker.PickerMediaType[] = pickerMediaType.get('Default')!.value;

  public convert(attributes: OriginAttribute[]): void {
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'mediaTypes':
          this.mediaTypes =
            pickerMediaType.get(attribute.currentValue)?.value ?? pickerMediaType.get('Default')!.value;
          break;
        default:
          break;
      }
    })
  }
}