/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { TabBarType } from '@ohos/commonbusiness';

export interface TabBarData {
  id: TabBarType;
  title: ResourceStr;
  icon: Resource;
}

export const TABS_LIST: TabBarData[] = [
  {
    id: TabBarType.HOME,
    icon: $r('sys.symbol.archivebox_fill'),
    title: $r('app.string.tab_home'),
  },
  {
    id: TabBarType.SAMPLE,
    icon: $r('sys.symbol.scenes'),
    title: $r('app.string.tab_sample'),
  },
  {
    id: TabBarType.PRACTICE,
    icon: $r('sys.symbol.discover_fill'),
    title: $r('app.string.tab_practice'),
  },
  {
    id: TabBarType.MINE,
    icon: $r('sys.symbol.person_crop_circle_fill_1'),
    title: $r('app.string.tab_mine'),
  },
]