/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { AttributeTypeEnum } from './AttributeTypeEnum';

@Observed
export abstract class Attribute {
  public name: string;
  public disPlayName: ResourceStr;
  public currentValue: string;
  public type: AttributeTypeEnum;
  public enable: boolean;

  constructor(name: string, disPlayName: ResourceStr, currentValue: string, type: AttributeTypeEnum) {
    this.name = name;
    this.disPlayName = disPlayName;
    this.currentValue = currentValue;
    this.type = type;
    this.enable = true;
  }
}

export class OriginAttribute {
  public name: string = '';
  public displayName: ResourceStr = '';
  public type: AttributeTypeEnum = AttributeTypeEnum.SELECT;
  public values: string [] = [];
  public leftRange: number = 0;
  public rightRange: number = 0;
  public step: number = 1;
  public currentValue: string = '';
}