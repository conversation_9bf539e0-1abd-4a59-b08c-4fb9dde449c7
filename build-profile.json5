{
  "app": {
    "signingConfigs": [],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.2(14)",
        "runtimeOS": "HarmonyOS",
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "phone",
      "srcPath": "./products/phone",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "mine",
      "srcPath": "./features/mine",
    },
    {
      "name": "exploration",
      "srcPath": "./features/exploration",
    },
    {
      "name": "common",
      "srcPath": "./common",
    },
    {
      "name": "componentlibrary",
      "srcPath": "./features/componentlibrary",
    },
    {
      "name": "devpractices",
      "srcPath": "./features/devpractices",
    },
    {
      "name": "commonbusiness",
      "srcPath": "./features/commonbusiness",
    },
    {
      "name": "wearable",
      "srcPath": "./products/wearable",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}