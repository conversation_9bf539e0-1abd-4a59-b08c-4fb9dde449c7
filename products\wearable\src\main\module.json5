{
  "module": {
    "name": "wearable",
    "type": "entry",
    "description": "$string:module_desc",
    "mainElement": "WearableAbility",
    "deviceTypes": [
      "wearable"
    ],
    "deliveryWithInstall": true,
    "installationFree": false,
    "pages": "$profile:main_pages",
    "abilities": [
      {
        "name": "WearableAbility",
        "srcEntry": "./ets/wearableability/WearableAbility.ets",
        "description": "$string:WearableAbility_desc",
        "icon": "$media:layered_image",
        "label": "$string:WearableAbility_label",
        "startWindowIcon": "$media:ic_start_icon_800",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "skills": [
          {
            "entities": [
              "entity.system.home"
            ],
            "actions": [
              "action.system.home"
            ]
          }
        ]
      }
    ],
    "extensionAbilities": [
      {
        "name": "WearableBackupAbility",
        "srcEntry": "./ets/wearablebackupability/WearableBackupAbility.ets",
        "type": "backup",
        "exported": false,
        "metadata": [
          {
            "name": "ohos.extension.backup",
            "resource": "$profile:backup_config"
          }
        ],
      }
    ]
  }
}