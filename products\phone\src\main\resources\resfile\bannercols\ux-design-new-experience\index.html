<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>HarmonyOS UX设计新体验</title>
  <link rel="stylesheet" href="./index.css" />
  <link rel="stylesheet" href="../common/css/multi.css">
</head>

<body>
  <div class="main-content">
    <!-- header -->
    <div class="header chapter">
      <div class="title">HarmonyOS UX设计新体验</div>
      <div class="desc">
        HarmonyOS
        多设备设计指南与重点特性规范，为你提供面向垂类场景、全端侧的针对性设计建议。
        并有简单易用的控件助力你打造高端精致的 HarmonyOS
        应用新体验，与你共同构建一个和谐的数字世界。
      </div>
      <div class="content">
        <img class="content-img" id="image" src="./image/design.png" alt="" />
      </div>
    </div>

    <!-- swiper -->
    <div class="swiper-wrap chapter">
      <div class="title">HarmonyOS 特征设计指南</div>
      <div class="desc">
        提供HarmonyOS 完整设计规范，以及创新特性的适配规范，让你快速构建出
        HarmonyOS 全场景设备的创新体验。
      </div>
      <div class="swiper">
        <div class="card-wrap">
          <div class="card-item">
            <img class="card-icon" src="./image/swiperItem1.png" alt="" draggable="false" />
            <div class="card-title">新控件</div>
            <div class="card-desc">
              提供基于 HarmonyOS
              版本风格的控件样式及设计规范，帮助你快速了解控件设计和基础能力。
            </div>
          </div>
          <div class="card-item">
            <img class="card-icon" src="./image/swiperItem2.png" alt="" draggable="false" />
            <div class="card-title">实况窗设计</div>
            <div class="card-desc">
              实况窗设计包含了卡片态、胶囊态、沉浸态，帮助用户聚焦进行中任务、方便快速查看和即时处理。
            </div>
          </div>
          <div class="card-item">
            <img class="card-icon" src="./image/swiperItem4.png" alt="" draggable="false" />
            <div class="card-title">一键登录</div>
            <div class="card-desc">
              华为账号提供登录设计规范，保障 HarmonyOS
              应用拥有简单易用、高效一致、快速安全的登录体验。
            </div>
          </div>
          <div class="card-item">
            <img class="card-icon" src="./image/swiperItem5.png" alt="" draggable="false" />
            <div class="card-title">元服务设计</div>
            <div class="card-desc">
              轻量高效、即点即用，多形态的组件构成样式为用户提供丰富便捷的应用服务。
            </div>
          </div>
          <div class="card-item">
            <img class="card-icon" src="./image/swiperItem6.png" alt="" draggable="false" />
            <div class="card-title">多窗</div>
            <div class="card-desc">
              包含悬浮窗、分屏不同的窗口形态，为你提供灵活高效的多任务并行体验。
            </div>
          </div>
          <div class="card-item">
            <img class="card-icon" src="./image/swiperItem8.png" alt="" draggable="false" />
            <div class="card-title">分享</div>
            <div class="card-desc">
              为各场景的内容分享体验提供设计规范，帮助你了解系统分享能力。
            </div>
          </div>
        </div>
        <div class="swiper-pagination">
          <span class="swiper-point swiper-point-selected" style="color-scheme: light dark;"></span>
          <span class="swiper-point" style="color-scheme: light dark;"></span>
          <span class="swiper-point" style="color-scheme: light dark;"></span>
          <span class="swiper-point" style="color-scheme: light dark;"></span>
          <span class="swiper-point" style="color-scheme: light dark;"></span>
          <span class="swiper-point" style="color-scheme: light dark;"></span>
        </div>
      </div>
    </div>

    <!-- multi-device -->
    <div class="multi-device chapter">
      <div class="title">多设备响应式设计</div>
      <div class="desc">
        HarmonyOS
        应用设计支持适配不同的屏幕尺寸和设备类型。保持多设备体验的连续性，降低你的工作量和维护成本。
      </div>
      <div style="margin-bottom: 30px;">
        <a class="jump-link jump-link-dark" href="article_uxexperirnce_1"  style="color-scheme: light dark;">了解详情</a>
      </div>
      <img style="display: none;" src="./image/multi_device.png" alt="" />
    </div>

    <!-- card-bg -->
    <div class="card-bg">
      <div class="bg-mode">
        <img src="./image/card_bg_1.png" alt="" class="bg-mode-img" />
        <div class="text-content font-color-white">
          <div class="title">应用UX体验标准</div>
          <div class="desc">
            本标准从影响用户体验的各个维度定义了相应测试规范，规定了应用需达到的基础体验要求，用于引导应用的设计与开发，以保证应用良好的使用体验。
          </div>
          <a class="jump-link" href="article_uxexperirnce_2">了解详情</a>
        </div>
      </div>
      <div class="bg-mode">
        <img src="./image/card_bg_2.png" alt="" class="bg-mode-img" />
        <div class="text-content">
          <div class="title" style="color-scheme: light dark;">多设备典型场景设计案例</div>
          <div class="desc" style="color-scheme: light dark;">
            为你提供适合特征型场景的界面设计样式、方便你结合应用的业务场景，进行最佳界面适配和创新设计。
          </div>
          <a class="jump-link" href="article_uxexperirnce_3">了解详情</a>
        </div>
      </div>
      <div class="bg-mode">
        <img src="./image/card_bg_3.png" alt="" class="bg-mode-img bg-mode-img-margin" id="bg_mode_img_design" />
        <div class="text-content">
          <div class="title" style="color-scheme: light dark;">设计资源</div>
          <div class="desc" style="color-scheme: light dark;">
            为你提供多种效率组件和界面模版，以及不断更新的设计资源库，包含图标、色彩、文字、音效等丰富的资源。
          </div>
          <ul class="link-group" style="color-scheme: light dark;">
            <li class="link-group-item active">
              字体 Fonts
            </li>
            <li class="link-group-item">
              组件 Components
            </li>
            <li class="link-group-item">
              图标 Icons
            </li>
          </ul>
          <a class="jump-link media-display-lg" href="article_uxexperirnce_4">了解详情</a>
        </div>
        <div class="media-display-sm" style="color-scheme: light dark;">
          <a class="jump-link" href="article_uxexperirnce_4">了解详情</a>
        </div>
      </div>
    </div>

    <!-- footer -->
    <div class="footer">
      <img class="footerImg"
        src="../common/image/f_icon.png"
        alt="" />
    </div>
  </div>
</body>
<script src="../common/js/banner.js"></script>
<script type="module" src="./index.js"></script>

</html>