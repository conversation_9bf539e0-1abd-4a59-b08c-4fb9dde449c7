/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { OriginAttribute } from '../../../viewmodel/Attribute';
import { CommonDescriptor } from '../../../viewmodel/CommonDescriptor';
import {
  canLoopMapData,
  itemHeightMapData,
} from '../entity/TextDialogAttributeMapping';

@Observed
export class TextPickerDialogDescriptor extends CommonDescriptor {
  public selected: number | number[] = 0;
  public canLoop: boolean = canLoopMapData.get('Default')!.value as boolean;
  public itemHeight: number = itemHeightMapData.get('Default')!.value as number;

  public convert(attributes: OriginAttribute[]): void {
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'canLoop':
          this.canLoop = JSON.parse(attribute.currentValue) ?? this.canLoop;
          break;
        case 'itemHeight':
          this.itemHeight = Number(attribute.currentValue) ?? this.itemHeight;
          break;
        default:
          break;
      }
    });
  }
}