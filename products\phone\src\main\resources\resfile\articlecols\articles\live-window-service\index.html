<!DOCTYPE html>
<html lang="zh-cn" xml:lang="zh-cn">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0, user-scalable=0" />
  <title>小窗口大世界，智享实况窗服务</title>
  <link rel="stylesheet" href="../../common/dist/main.css">
  <link rel="stylesheet" href="../../common/css/article.css">
</head>

<body>
  <div class="nested0" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002124617272"><a name="ZH-CN_TOPIC_0000002124617272"></a><a
      name="ZH-CN_TOPIC_0000002124617272"></a>
    <h1 class="topictitle1"><span class="topictitlenumber1">1</span> 小窗口大世界，智享实况窗服务</h1>
    <div class="topicbody" id="body39451090"></div>
    <div>
      <ul class="ullinks">
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002160021397">1.1 实时掌控重要信息变化</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002160021409">1.2 丰富的场景支持</a></strong><br>
        </li>
        <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002124781472">1.3 热门场景案例</a></strong><br>
        </li>
      </ul>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002160021397"><a
        name="ZH-CN_TOPIC_0000002160021397"></a><a name="ZH-CN_TOPIC_0000002160021397"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.1</span> <strong
          id="b14275152645017">实时掌控重要信息变化</strong></h2>
      <div class="topicbody" id="body0000002160021397">
        <p id="ZH-CN_TOPIC_0000002160021397__p134411219144510">
          移动互联网时代，各种以用户为中心的App如春笋般涌现，满足了用户在购物、导航、娱乐等不同场景下的需求，不同程度上丰富了用户的日常生活。然而，随着App种类的激增，用户在享受多样化服务的同时，也面临着多任务管理的挑战。
        </p>
        <p id="ZH-CN_TOPIC_0000002160021397__p1583962312469">
          比如，用户正沉浸在游戏或追剧中时，能否想起并打开App查看一下：不久前点的外卖是否已上路？网约车是否已到达？餐厅预订的餐品是否已准备好?
          在多应用进程并行使用的情况下，这些重要的服务很容易被忽略，从而影响到用户的日常生活质量与效率。</p>
        <div class="fignone" id="ZH-CN_TOPIC_0000002160021397__fig11971194112150"><span class="figcap"><span
              class="figurenumber">图1-1</span> 通知中心和锁屏</span><br><img
            id="ZH-CN_TOPIC_0000002160021397__image183191328111816" src="ManulImages/1.png"></div>
        <p id="ZH-CN_TOPIC_0000002160021397__p6149192364619">
          这时候,就可以使用实况窗服务。该服务支持应用将订单或服务的实时状态信息变化在设备的熄屏、锁屏、通知中心、状态栏等关键界面展示，并对展示信息的生命周期、用户界面UI效果等进行管理，在特定的情况下还可以震动或发出特定声音来提醒用户，帮助用户聚焦正在进行的任务，方便查看和即时处理通知内容。
        </p>
        <div class="fignone" id="ZH-CN_TOPIC_0000002160021397__fig15532111013178"><span class="figcap"><span
              class="figurenumber">图1-2</span> 状态栏</span><br><img id="ZH-CN_TOPIC_0000002160021397__image174515480181"
            src="ManulImages/2.png"></div>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002160021409"><a
        name="ZH-CN_TOPIC_0000002160021409"></a><a name="ZH-CN_TOPIC_0000002160021409"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.2</span> <strong id="b3992205211571">丰富的场景支持</strong>
      </h2>
      <div class="topicbody" id="body0000002160021409">
        <p id="ZH-CN_TOPIC_0000002160021409__p2511263255">实况窗场景适用于出行打车、高铁、送餐等场景，具体可支持对接的场景如下表所示，更详细的场景说明请参考<a
            href="article_liveview_1"
            target="_blank" rel="noopener noreferrer">这里</a>。</p>

        <div class="tablenoborder">
          <table cellpadding="4" cellspacing="0" summary="" id="ZH-CN_TOPIC_0000002160021409__table1464795854212"
            frame="border" border="1" rules="all">
            <thead align="left">
              <tr id="ZH-CN_TOPIC_0000002160021409__row56471358154217">
                <th align="left" class="cellrowborder" valign="top" width="20%" id="mcps1.4.6.3.2.1.4.1.1">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1464805894217"><strong
                      id="ZH-CN_TOPIC_0000002160021409__b95241015104315">场景类型</strong></p>
                </th>
                <th align="left" class="cellrowborder" valign="top" width="40%" id="mcps1.4.6.3.2.1.4.1.2">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1964875811424"><strong
                      id="ZH-CN_TOPIC_0000002160021409__b15599524114314">适用范围</strong></p>
                </th>
                <th align="left" class="cellrowborder" valign="top" width="40%" id="mcps1.4.6.3.2.1.4.1.3">
                  <p id="ZH-CN_TOPIC_0000002160021409__p76981649104414"><strong
                      id="ZH-CN_TOPIC_0000002160021409__b3671321182712">适用范围</strong></p>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr id="ZH-CN_TOPIC_0000002160021409__row5648145812425">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p7121915456">出行打车</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1812714451">用户线上约车后，向用户展示司机接驾等待时间、行程中的剩余距离和时间等信息。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1686111113274">适用于网约车、出租车、拼车、顺风车等场景。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row66481958194218">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p131261124516">即时配送</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p181281184510">指配送员将餐品、商品送达到用户指定地点的业务场景，通常在较短时间内完成配送环节。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1112121104520">适用于外卖、生鲜配送、同城配送等场景。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row564855812423">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p81215154512">航班</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p812311455">
                    用户主动关注某个航班时，向用户展示航班的关键变动，如航班开始登机、航班起飞、航班延误、航班取消、航班到达等关键场景。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p11210111451">适用于用户通过航班出行或者主动关注某个航班进展的场景。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row664855819421">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1312018454">高铁/火车</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p121211118457">用户通过高铁、火车出行，向用户展示检票口、座位号、车次信息及列车运行状态等信息。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p14125110453">适用于高铁出行、火车出行的场景。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row46481558104218">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p3129124514">排队</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p15123117457">需要通过排队叫号的方式，按顺序为用户提供服务的业务场景。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1712141184512">适用于办事大厅、医院、银行、餐饮等排队叫号能力场景。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row1264813589424">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1412814459">取餐</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p9121514452">指的是用户完成餐品/商品下单后，自行取餐或者取件的场景。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p161220114519">适用于餐饮线下取餐提醒，包括餐品排队情况、制作进度、取餐提醒等。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row864835884218">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p181215144512">赛事比分</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p4125104514">展示比赛双方成绩变化情况。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p2123111453">适用于游戏赛事、体育赛事等展示比分变化情况的场景。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row2060014274312">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p151281104518">共享租赁</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p131231154513">用户使用临时租赁服务时，向用户展示实时租赁时长和费用等租赁状态信息的场景。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p71213119453">适用于共享单车、共享充电宝、停车场临时停车等场景。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row11895124515434">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p51311174510">计时</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p61351124515">用户在某个短时间段持续的正计时或任务前的倒计时场景。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p4131512456">适用于专注时刻、番茄时钟、抢票倒计时提醒场景，仅限于工具类应用申请（计时场景仅支持通过端侧创建与更新）。
                  </p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row212713486434">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p121361154519">运动锻炼</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p1132154511">运动过程中，向用户实时展示运动的时长和进度等信息。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p3131717451">适用于户外或室内的运动记录，如跑步、骑行等。</p>
                </td>
              </tr>
              <tr id="ZH-CN_TOPIC_0000002160021409__row411915509434">
                <td class="cellrowborder" valign="top" width="20%" headers="mcps1.4.6.3.2.1.4.1.1 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p16133124515">导航</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.2 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p121315154511">用户使用导航服务时，展示将要发生的路线变化。</p>
                </td>
                <td class="cellrowborder" valign="top" width="40%" headers="mcps1.4.6.3.2.1.4.1.3 ">
                  <p id="ZH-CN_TOPIC_0000002160021409__p4132184511">适用于步行导航、骑行导航、车辆导航。</p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div></div>
    </div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002124781472"><a
        name="ZH-CN_TOPIC_0000002124781472"></a><a name="ZH-CN_TOPIC_0000002124781472"></a>
      <h2 class="topictitle2"><span class="topictitlenumber2">1.3</span> 热门场景案例</h2>
      <div class="topicbody" id="body0000002124781472">
        <p id="ZH-CN_TOPIC_0000002124781472__p620215165513">
          在设计特定应用场景的实况窗时，开发者需要考虑应用服务进程中需要设置提醒的关键节点与呈现的内容信息，这也是用户在使用实况窗过程中最关注的部分,在这里我们用三个热门场景来举例说明。</p>
      </div>
      <div></div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002134085076"><a
          name="ZH-CN_TOPIC_0000002134085076"></a><a name="ZH-CN_TOPIC_0000002134085076"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.1</span> 即时配送场景</h3>
        <div class="topicbody" id="body0000002134085076">
          <p id="ZH-CN_TOPIC_0000002134085076__p117631343151111">
            即时配送的关键节点可分为用户下单、等待用户支付、等待商家接单、商家已接单、骑手取货中、骑手取货中、骑手配送中、商品已送达、放入取餐柜，用户可以通过实况窗实时得知外卖的配送进度，而无需频繁点开应用详情页查看。</p>
          <div class="fignone" id="ZH-CN_TOPIC_0000002134085076__fig1956242292"><span class="figcap"><span
                class="figurenumber">图1-3</span> 即时配送节点图</span><br><img
              id="ZH-CN_TOPIC_0000002134085076__image357748295" src="ManulImages/3.png"></div>
          <p id="ZH-CN_TOPIC_0000002134085076__p181615273509">详细场景节点设计可以参考<a
              href="article_liveview_2"
              target="_blank" rel="noopener noreferrer">即时配送</a><strong
              id="ZH-CN_TOPIC_0000002134085076__b166632036125812">。</strong></p>
        </div>
      </div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002169206321"><a
          name="ZH-CN_TOPIC_0000002169206321"></a><a name="ZH-CN_TOPIC_0000002169206321"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.2</span> 航班场景</h3>
        <div class="topicbody" id="body0000002169206321">
          <p id="ZH-CN_TOPIC_0000002169206321__p184292049122118">
            航班场景的关键节点分为未值机、已值机、开始登机、催促登机、已登机、已起飞、已抵达、延误、取消、登机口变更,实况窗在各个节点会显示不同的内容,同时提醒用户作出对应的响应<strong
              id="ZH-CN_TOPIC_0000002169206321__b1541717233920">。</strong></p>
          <div class="fignone" id="ZH-CN_TOPIC_0000002169206321__fig12289174712307"><span class="figcap"><span
                class="figurenumber">图1-4</span> 航班节点图</span><br><img
              id="ZH-CN_TOPIC_0000002169206321__image128994743018" src="ManulImages/4.png"></div>
          <p id="ZH-CN_TOPIC_0000002169206321__p65305347528">延误取消登机口变更等特殊情况节点：</p>
          <div class="fignone" id="ZH-CN_TOPIC_0000002169206321__fig53601746331"><span class="figcap"><span
                class="figurenumber">图1-5</span> 特殊情况节点图</span><br><img
              id="ZH-CN_TOPIC_0000002169206321__image236010433314" src="ManulImages/5.png"></div>
          <p id="ZH-CN_TOPIC_0000002169206321__p16203523363">更加详细的航班场景节点设计可以参考<a
              href="article_liveview_3"
              target="_blank" rel="noopener noreferrer">航班场景</a><strong
              id="ZH-CN_TOPIC_0000002169206321__b12502194610589">。</strong></p>
        </div>
      </div>
      <div class="nested2" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002169324749"><a
          name="ZH-CN_TOPIC_0000002169324749"></a><a name="ZH-CN_TOPIC_0000002169324749"></a>
        <h3 class="topictitle3"><span class="topictitlenumber3">1.3.3</span> 打车出行场景</h3>
        <div class="topicbody" id="body0000002169324749">
          <p id="ZH-CN_TOPIC_0000002169324749__p1186822332312">打车出行按照用户是否立即出发，分为即时出行场景和预约出行场景。</p>
          <p id="ZH-CN_TOPIC_0000002169324749__p167462305411">
            场景节点主要分为叫车、排队、司机正在完成上一单、司机本单接驾中、即将到达上车点、司机已到达、开始前往目的地、到达目的地。</p>
          <p id="ZH-CN_TOPIC_0000002169324749__p97641934191418">用户可以在任意节点了解当前车辆和行程信息。</p>
          <p id="ZH-CN_TOPIC_0000002169324749__p1342811192194">预约出行场景，需计划出发前20分钟开始展示实况窗提醒用户。</p>
          <div class="fignone" id="ZH-CN_TOPIC_0000002169324749__fig34926422435"><span class="figcap"><span
                class="figurenumber">图1-6</span> 打车出行节点图</span><br><img
              id="ZH-CN_TOPIC_0000002169324749__image11492144211438" src="ManulImages/6.png"></div>
          <p id="ZH-CN_TOPIC_0000002169324749__p59059346144">更加详细的节点设计可以参考<a
              href="article_liveview_4"
              target="_blank" rel="noopener noreferrer">打车出行场景</a><strong
              id="ZH-CN_TOPIC_0000002169324749__b03118365119">。</strong></p>
          <div class="note" id="ZH-CN_TOPIC_0000002169324749__note1352816562915"><img src=""><span class="notetitle">
            </span>
            <div class="notebody">
              <p id="ZH-CN_TOPIC_0000002169324749__p121834932912">以上三种场景均有相关Sample代码：<a
                  href="gitee_liveview_1"
                  target="_blank" rel="noopener noreferrer">代码地址</a>，实况窗更加丰富内容可以参考<a
                  href="article_liveview_5"
                  target="_blank" rel="noopener noreferrer">实况窗服务</a>文章。</p>
            </div>
          </div>
          <p id="ZH-CN_TOPIC_0000002169324749__p74001138026"></p>
        </div>
      </div>
    </div>
  </div>
</body>

<script type="module" src="../../common/js/changehref.js"></script>
<script type="module" src="../../common/dist/main.js"></script>

</html>