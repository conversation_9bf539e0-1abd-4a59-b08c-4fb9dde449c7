/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export class SampleDetailConstant {
  // Progress
  public static readonly PROGRESS_START: number = 0;
  public static readonly PROGRESS_FINISH: number = 100;
  // MaxLine
  public static readonly SUBTITLE_MAXLINE: number = 3;
  // The offset of the popup component relative to the display position defined by the placement setting.
  public static HOVER_POPUP_LEFT: number = 10;
}