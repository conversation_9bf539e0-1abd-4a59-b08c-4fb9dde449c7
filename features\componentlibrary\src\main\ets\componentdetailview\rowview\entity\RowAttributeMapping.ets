/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { CommonNumberMapping, CommonStringMapping } from '../../common/entity/CommonMapData';

class RowAlignMapping {
  public readonly code: string;
  public readonly value: VerticalAlign;

  constructor(code: string, value: VerticalAlign) {
    this.code = code;
    this.value = value;
  }
}

export const rowAlignMapData: Map<string, RowAlignMapping> = new Map([
  ['Top', new RowAlignMapping('VerticalAlign.Top', VerticalAlign.Top)],
  ['Center', new RowAlignMapping('VerticalAlign.Center', VerticalAlign.Center)],
  ['Bottom', new RowAlignMapping('VerticalAlign.Bottom', VerticalAlign.Bottom)],
  ['Default', new RowAlignMapping('VerticalAlign.Center', VerticalAlign.Center)],
]);

class RowJustifyContentMapping {
  public readonly code: string;
  public readonly value: FlexAlign;

  constructor(code: string, value: FlexAlign) {
    this.code = code;
    this.value = value;
  }
}

export const rowJustifyContentMapData: Map<string, RowJustifyContentMapping> = new Map([
  ['Start', new RowJustifyContentMapping('FlexAlign.Start', FlexAlign.Start)],
  ['Center', new RowJustifyContentMapping('FlexAlign.Center', FlexAlign.Center)],
  ['End', new RowJustifyContentMapping('FlexAlign.End', FlexAlign.End)],
  ['SpaceBetween', new RowJustifyContentMapping('FlexAlign.SpaceBetween', FlexAlign.SpaceBetween)],
  ['SpaceAround', new RowJustifyContentMapping('FlexAlign.SpaceAround', FlexAlign.SpaceAround)],
  ['SpaceEvenly', new RowJustifyContentMapping('FlexAlign.SpaceEvenly', FlexAlign.SpaceEvenly)],
  ['Default', new RowJustifyContentMapping('FlexAlign.Start', FlexAlign.Start)],
]);

export const rowSpaceMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('3', 3)],
]);

export const rowPaddingMapData: Map<string, CommonStringMapping> = new Map([
  ['Vertical', new CommonStringMapping('Vertical', 'Vertical')],
  ['Horizontal', new CommonStringMapping('Horizontal', 'Horizontal')],
  ['All', new CommonStringMapping('All', 'All')],
  ['Default', new CommonStringMapping('All', 'All')],
]);

export const paddingNumMapData: Map<string, CommonNumberMapping> = new Map([
  ['Default', new CommonNumberMapping('3', 3)],
]);