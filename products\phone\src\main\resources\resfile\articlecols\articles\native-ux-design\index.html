<!DOCTYPE html>
<html lang="zh-cn" xml:lang="zh-cn">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0, user-scalable=0" />
    <title>多端UX设计</title>
    <link rel="stylesheet" href="../../common/dist/main.css">
    <link rel="stylesheet" href="../../common/css/article.css">
  </head>

  <body>
    <div class="nested0" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002202815150"><a name="ZH-CN_TOPIC_0000002202815150"></a><a name="ZH-CN_TOPIC_0000002202815150"></a><h1 class="topictitle1"><span class="topictitlenumber1">1</span>   多端UX设计</h1>
    <div class="topicbody" id="body0000002160219684"></div>
    <div>
    <ul class="ullinks">
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002237735397">1.1  多端UX设计</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002237735393">1.2  为什么需要「 多端的UX设计 」</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002202655330">1.3  怎样做「 多端的UX设计 」</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002237655557">1.4  基础要求</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002202655326">1.5  响应式布局</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002237655553">1.6  增值体验</a></strong><br>
    </li>
    <li class="ulchildlink"><strong><a href="#ZH-CN_TOPIC_0000002202815154">1.7  典型案例</a></strong><br>
    </li>
    </ul>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002237735393"><a name="ZH-CN_TOPIC_0000002237735393"></a><a name="ZH-CN_TOPIC_0000002237735393"></a><h2 class="topictitle2"><span class="topictitlenumber2">1.2</span>   为什么需要「 多端的UX设计 」</h2>
    <div class="topicbody" id="body0000002129959102"><p id="ZH-CN_TOPIC_0000002237735393__p5921622101712">常见宽屏上的体验痛点：</p>
    <ul id="ZH-CN_TOPIC_0000002237735393__ul148210398465"><li id="ZH-CN_TOPIC_0000002237735393__li48213397467">大图大字体、信息量少、内容截断、内容变形错乱、不支持横屏、留白过大。</li><li id="ZH-CN_TOPIC_0000002237735393__li2620194284618">想要做好宽屏设计，但不知道怎样实现体验增值。</li></ul>
    <p id="ZH-CN_TOPIC_0000002237735393__p16908162312532"><img id="ZH-CN_TOPIC_0000002237735393__image19908823165315" src="ManulImages/4.png"></p>
    <p id="ZH-CN_TOPIC_0000002237735393__p482293185313"><img id="ZH-CN_TOPIC_0000002237735393__image9822331105314" src="ManulImages/5.png"></p>
    </div>
    <div></div></div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002202655330"><a name="ZH-CN_TOPIC_0000002202655330"></a><a name="ZH-CN_TOPIC_0000002202655330"></a><h2 class="topictitle2"><span class="topictitlenumber2">1.3</span>   怎样做「 多端的UX设计 」</h2>
    <div class="topicbody" id="body0000002165198857"><p id="ZH-CN_TOPIC_0000002202655330__ZH-CN_TOPIC_0000002202655330_mMcCpPsS_p8060118">了解多端的物理形态差异，及人因上的影响，考虑最佳的交互方式、信息量&amp;内容布局 基于宽屏上“更多显示”、“更加沉浸”、“更高效率”、“悬停创新”的设计原则，挖掘增值体验。</p>
    <p id="ZH-CN_TOPIC_0000002202655330__p2939115617523"><img id="ZH-CN_TOPIC_0000002202655330__image169396562521" src="ManulImages/7.png"></p>
    </div>
    <div></div></div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002237655557"><a name="ZH-CN_TOPIC_0000002237655557"></a><a name="ZH-CN_TOPIC_0000002237655557"></a><h2 class="topictitle2"><span class="topictitlenumber2">1.4</span>   基础要求</h2>
    <div class="topicbody" id="body0000002165320449"><p id="ZH-CN_TOPIC_0000002237655557__p1253811515555"><img id="ZH-CN_TOPIC_0000002237655557__image953885135515" src="ManulImages/8.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655557__ZH-CN_TOPIC_0000002237655557_mMcCpPsS_p8060118"><strong id="ZH-CN_TOPIC_0000002237655557__b16745223015">基础要求要求</strong></p>
    <p id="ZH-CN_TOPIC_0000002237655557__p866315015561"><img id="ZH-CN_TOPIC_0000002237655557__image566311065615" src="ManulImages/9.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655557__p18426105413546"><strong id="ZH-CN_TOPIC_0000002237655557__b145052483019">基础要求（应用UX体验标准）</strong></p>
    <p id="ZH-CN_TOPIC_0000002237655557__p732611016564"><img id="ZH-CN_TOPIC_0000002237655557__image1332612104564" src="ManulImages/10.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655557__p123351118125616"><img id="ZH-CN_TOPIC_0000002237655557__image4335111817567" src="ManulImages/11.png"></p>
    </div>
    <div></div></div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002202655326"><a name="ZH-CN_TOPIC_0000002202655326"></a><a name="ZH-CN_TOPIC_0000002202655326"></a><h2 class="topictitle2"><span class="topictitlenumber2">1.5</span>   响应式布局</h2>
    <div class="topicbody" id="body0000002129800918"><p id="ZH-CN_TOPIC_0000002202655326__p13232184955610"><img id="ZH-CN_TOPIC_0000002202655326__image19232174995614" src="ManulImages/12.png"></p>
    <p id="ZH-CN_TOPIC_0000002202655326__p1697116568560"><strong id="ZH-CN_TOPIC_0000002202655326__b7977117133020">响应式布局方法</strong></p>
    <p id="ZH-CN_TOPIC_0000002202655326__p133758186576"><img id="ZH-CN_TOPIC_0000002202655326__image1037591813571" src="ManulImages/13.png"></p>
    <p style="color:#F79646;" id="ZH-CN_TOPIC_0000002202655326__p1851763782217"><strong id="ZH-CN_TOPIC_0000002202655326__b1426525716221">案例</strong></p>
    <p id="ZH-CN_TOPIC_0000002202655326__p5867182117577">沉浸广告图的自适应延伸布局：影音娱乐、电商、生活服务、金融理财等需要打造沉浸感或营销氛围的场景，可使用沉浸广告图 背景和内容分层，背景横向铺满，广告元素延伸布局，横屏不超过0.5倍屏幕高度，竖屏不超过0.4倍屏幕高度。</p>
    <p id="ZH-CN_TOPIC_0000002202655326__p255016508592"><img id="ZH-CN_TOPIC_0000002202655326__image555085065917" src="ManulImages/14.png"></p>
    <p id="ZH-CN_TOPIC_0000002202655326__p55083203589">宽屏设备上，沉浸式Banner切换为卡片Banner：</p>
    <p id="ZH-CN_TOPIC_0000002202655326__p11646125817590"><img id="ZH-CN_TOPIC_0000002202655326__image5646058115912" src="ManulImages/15.png"></p>
    <p id="ZH-CN_TOPIC_0000002202655326__p1621214581">卡片广告图的延伸布局+挪移布局：</p>
    <p id="ZH-CN_TOPIC_0000002202655326__p62854234016"><img id="ZH-CN_TOPIC_0000002202655326__image9285192317010" src="ManulImages/16.png"></p>
    </div>
    <div></div></div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002237655553"><a name="ZH-CN_TOPIC_0000002237655553"></a><a name="ZH-CN_TOPIC_0000002237655553"></a><h2 class="topictitle2"><span class="topictitlenumber2">1.6</span>   增值体验</h2>
    <div class="topicbody" id="body0000002129959106"><p id="ZH-CN_TOPIC_0000002237655553__p1462040323"><img id="ZH-CN_TOPIC_0000002237655553__image86264018215" src="ManulImages/20.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p15779648425"><strong id="ZH-CN_TOPIC_0000002237655553__b24741618134419">增值体验的设计方法</strong></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p1681635017129"><img id="ZH-CN_TOPIC_0000002237655553__image481615061212" src="ManulImages/21.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p1118817141238"><strong style="color:#F79646;" id="ZH-CN_TOPIC_0000002237655553__b81881114162316">显示更多</strong></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p2531125719213">边看边评：在宽屏设备上进行图文阅读时，右侧提供评论、相关推荐、关键词解析等更多相关辅助信息。</p>
    <p id="ZH-CN_TOPIC_0000002237655553__p2062075713123"><img id="ZH-CN_TOPIC_0000002237655553__image162095716129" src="ManulImages/22.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p112253534814"><strong style="color:#F79646;" id="ZH-CN_TOPIC_0000002237655553__b161222035114815">更加沉浸</strong></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p78839571522">沉浸观影 折叠屏上全屏播放视频不旋转，不中断观影体验。</p>
    <p id="ZH-CN_TOPIC_0000002237655553__p81715181311"><img id="ZH-CN_TOPIC_0000002237655553__image1117165121311" src="ManulImages/23.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p2111238947">弹幕吸顶：弹幕尽量在留黑的位置显示，减少对视频画面的遮挡。</p>
    <p id="ZH-CN_TOPIC_0000002237655553__p155581512111315"><img id="ZH-CN_TOPIC_0000002237655553__image75581812121313" src="ManulImages/24.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p429843818412">侧边面板：播放视频时，在侧边面板内执行其他相关操作，视频画面被推挤，避免对视频画面的遮挡。</p>
    <p id="ZH-CN_TOPIC_0000002237655553__p173221319161317"><img id="ZH-CN_TOPIC_0000002237655553__image1132213192135" src="ManulImages/25.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p53641550122313"><strong style="color:#F79646;" id="ZH-CN_TOPIC_0000002237655553__b15364205017234">更高效率</strong></p>
    <ul id="ZH-CN_TOPIC_0000002237655553__ul73059033912"><li id="ZH-CN_TOPIC_0000002237655553__li130515023910">高效分栏：金融、电商、生活服务、新闻资讯类应用部分页面，通过分栏提升效率。</li></ul>
    <p id="ZH-CN_TOPIC_0000002237655553__p74023288132"><img id="ZH-CN_TOPIC_0000002237655553__image11402172851318" src="ManulImages/26.png"></p>
    <ul id="ZH-CN_TOPIC_0000002237655553__ul88671649143911"><li id="ZH-CN_TOPIC_0000002237655553__li286754903920">应用内分屏：办公类场景，应用内分屏实现文档协同；IM对话场景，打开网页、元服务、文档、通话时提供应用内分屏，实现边聊边看体验；购物场景时通过应用内分屏，实现购物比价功能。</li></ul>
    <p id="ZH-CN_TOPIC_0000002237655553__p189721736191318"><img id="ZH-CN_TOPIC_0000002237655553__image5972636131312" src="ManulImages/27.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__ZH-CN_TOPIC_0000002237655553_mMcCpPsS_p12419192313242"><strong style="color:#F79646;" id="ZH-CN_TOPIC_0000002237655553__ZH-CN_TOPIC_0000002237655553_mMcCpPsS_b7266201922412">悬停适配</strong></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p1158610401146">交互类控件，在下半屏易交互区域显示：</p>
    <p id="ZH-CN_TOPIC_0000002237655553__p1133938101813"><img id="ZH-CN_TOPIC_0000002237655553__image3339984183" src="ManulImages/33.png"></p>
    <p id="ZH-CN_TOPIC_0000002237655553__p189951231061">上下文关联的控件，和其触发元素在同一侧屏幕内显示：</p>
    <p id="ZH-CN_TOPIC_0000002237655553__p765411153186"><img id="ZH-CN_TOPIC_0000002237655553__image1265441591820" src="ManulImages/34.png"></p>
    </div>
    <div></div></div>
    <div class="nested1" xml:lang="zh-cn" id="ZH-CN_TOPIC_0000002202815154"><a name="ZH-CN_TOPIC_0000002202815154"></a><a name="ZH-CN_TOPIC_0000002202815154"></a><h2 class="topictitle2"><span class="topictitlenumber2">1.7</span>   典型案例</h2>
    <div class="topicbody" id="body0000002165198861"><p id="ZH-CN_TOPIC_0000002202815154__p1068192110197"><img id="ZH-CN_TOPIC_0000002202815154__image16677214191" src="ManulImages/35.png"></p>
    <p id="ZH-CN_TOPIC_0000002202815154__p5211611154815"><strong style="color:#F79646;" id="ZH-CN_TOPIC_0000002202815154__b17136131218481">典型案例-电商购物</strong></p>
    <p id="ZH-CN_TOPIC_0000002202815154__p662342184417"><img id="ZH-CN_TOPIC_0000002202815154__image251185071919" src="ManulImages/38.png"></p>
    <p id="ZH-CN_TOPIC_0000002202815154__p653619565199"><img id="ZH-CN_TOPIC_0000002202815154__image1653665610198" src="ManulImages/39.png"></p>
    </div>
    <div></div></div>
    </div>
  </body>

  <script type="module" src="../../common/dist/main.js"></script>
</html>
