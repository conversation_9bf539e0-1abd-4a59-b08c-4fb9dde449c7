/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

@Observed
export class GlobalInfoModel {
  public foldExpanded: boolean = false;
  public currentBreakpoint: BreakpointTypeEnum = BreakpointTypeEnum.MD;
  public naviIndicatorHeight: number = 0;
  public statusBarHeight: number = 0;
  public decorHeight: number = 0;
  public deviceHeight: number = 0;
  public deviceWidth: number = 0;
}

export enum BreakpointTypeEnum {
  XS = 'xs',
  SM = 'sm',
  MD = 'md',
  LG = 'lg',
  XL = 'xl',
}