{"code": 200, "message": "Success", "data": [{"id": 35, "categoryType": 4, "order": 1, "sampleDetail": [{"id": 60, "title": "HarmonyOS代码工坊", "desc": "HarmonyOS代码工坊App是华为官方出品的一款大型开源开发示范应用。", "preInstalled": true, "sampleType": "commonClient", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/hdc/hmosworld_all_device.png", "originalUrl": "https://gitee.com/harmonyos_samples/sample_in_harmonyos/blob/master/README.md", "moduleName": "", "abilityName": "", "order": 1}, {"id": 63, "title": "HarmonyOS代码工坊(手表版)", "desc": "HarmonyOS代码工坊手表开发案例。", "preInstalled": true, "sampleType": "wearableClient", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/hdc/hmosworld_watch.png", "originalUrl": "https://gitee.com/harmonyos_samples/sample_in_harmonyos/blob/master/README.md", "moduleName": "", "abilityName": "", "order": 7}, {"id": 61, "title": "三折叠，怎么折都有面", "desc": "本示例主要使用断点监听和sidebarContainer组件、navigation组件相结合的方式，实现了商务办公类差异化的多场景响应式变化效果。", "preInstalled": true, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/hdc/sample_business.png", "originalUrl": "https://gitee.com/harmonyos_samples/MultiBusinessOffice/blob/br_release_hmosworld_new/README.md", "moduleName": "multibusinesssample", "abilityName": "MultibusinesssampleAbility", "order": 2}, {"id": 62, "title": "扩感导航，视野更清晰", "desc": "锁屏沉浸实况窗在锁屏界面展示重要信息，让用户无需进入应用即可获取活动状态，适用于实时性要求高的场景。", "preInstalled": true, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/hdc/sample_liveviewlockscreen.png", "originalUrl": "https://gitee.com/harmonyos_samples/LiveViewLockScreen/blob/br_release_hmos/README.md", "moduleName": "liveviewlockscreensample", "abilityName": "LiveviewlockscreensampleAbility", "order": 3}, {"id": 64, "title": "碰一碰视频快速分享", "desc": "本示例利用Share Kit与App Linking的结合，实现了快速跨设备分享视频并直接进入应用内视频播放页面的功能。", "preInstalled": true, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/hdc/sample_knockshare.png", "originalUrl": "https://gitee.com/harmonyos_samples/knock-share/blob/br_release_hmos/README.md", "moduleName": "knocksharesample", "abilityName": "KnocksharesampleAbility", "order": 4}, {"id": 65, "title": "视频投播更便捷", "desc": "本实例基于播控中心和系统投播实现完整的视频投播功能，包含投播和播控基础控制：设备切换、集数切换、音量增减、进度切换。", "preInstalled": true, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/hdc/sample_videocast.png", "originalUrl": "https://gitee.com/harmonyos_samples/VideoCast/blob/br_release_hmos/README.md", "moduleName": "videocastsample", "abilityName": "VideocastsampleAbility", "order": 5}, {"id": 66, "title": "跨设备内容编辑新体验", "desc": "本示例基于应用接续、分布式数据对象、分布式文件系统、跨设备互通等功能，实现文本图片数据跨设备交互及接续。", "preInstalled": true, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/hdc/sample_continuepublic.png", "originalUrl": "https://gitee.com/harmonyos_samples/ContinuePublish/blob/br_release_hmos/README.md", "moduleName": "continuepublishsample", "abilityName": "ContinuepublishsampleAbility", "order": 6}]}, {"id": 4, "categoryType": 1, "order": 1, "sampleDetail": [{"id": 16, "title": "一多导航栏", "desc": "本示例基于自适应布局和响应式布局，实现多设备上的分级导航栏效果。在sm、md断点下，展示为底部页签和顶部页签；在lg断点下，展示为侧边页签和顶部页签；在xl断点下，展示为侧边栏分级导航。为开发者提供分级导航栏的开发方案。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/multidevice/sample_multi_nav_bar.png", "originalUrl": "https://gitee.com/harmonyos_samples/multi-nav-bar/blob/br_release_hmosworld_new/README.md", "moduleName": "multinavbarsample", "abilityName": "MultinavbarsampleAbility", "order": 1}]}, {"id": 18, "categoryType": 1, "order": 2, "sampleDetail": [{"id": 33, "title": "一多便捷生活", "desc": "本篇Sample基于自适应布局和响应式布局，实现一次开发，多端部署的便捷生活页面，并根据手机、折叠屏、平板以及2in1不同的设备尺寸实现对应页面。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/multidevice/sample_multi_convenient_life.png", "originalUrl": "https://gitee.com/harmonyos_samples/multi-convenient-life/blob/br_release_hmosworld_new/README.md", "moduleName": "multiconvinientlifesample", "abilityName": "MulticonvinientlifesampleAbility", "order": 1}]}, {"id": 19, "categoryType": 1, "order": 3, "sampleDetail": [{"id": 34, "title": "一多移动支付", "desc": "本篇Sample基于Scan Kit中的默认界面扫码能力与码图生成能力实现移动支付应用中常见的扫一扫和收付款功能。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/multidevice/sample_multi_mobile_payment.png", "originalUrl": "https://gitee.com/harmonyos_samples/multi-mobile-payment/blob/br_release_hmosworld_new/README.md", "moduleName": "multimobilepaymentsample", "abilityName": "MultimobilepaymentsampleAbility", "order": 1}]}, {"id": 21, "categoryType": 1, "order": 5, "sampleDetail": [{"id": 45, "title": "一多新闻阅读", "desc": "本示例基于自适应布局和响应式布局，实现一次开发，多端部署的新闻阅读页面。根据手机、折叠屏以及平板不同的设备尺寸实现对应页面。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/multidevice/sample_multi_news_read.png", "originalUrl": "https://gitee.com/harmonyos_samples/multi-news-read/blob/br_release_hmosworld_new/README.md", "moduleName": "multinewsreadsample", "abilityName": "MultinewsreadsampleAbility", "order": 1}]}, {"id": 22, "categoryType": 1, "order": 6, "sampleDetail": [{"id": 55, "title": "一多旅行住宿", "desc": "本示例主要使用栅格布局和List组件相结合的方式，实现了旅行住宿差异化的多场景响应式变化效果。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/multidevice/sample_multi_travel_accommodation.png", "originalUrl": "https://gitee.com/harmonyos_samples/multi-travel-accommodation/blob/br_release_hmosworld_new/README.md", "moduleName": "multitravelsample", "abilityName": "MultitravelsampleAbility", "order": 1}]}, {"id": 24, "categoryType": 1, "order": 8, "sampleDetail": [{"id": 57, "title": "一多分栏控件", "desc": "本示例通过使用SideBarContainer组件与Navigation组件，实现了多场景下，一多分栏控件的响应式变化效果。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/multidevice/sample_multi_columns.png", "originalUrl": "https://gitee.com/harmonyos_samples/multi-columns/blob/br_release_hmosworld_new/README.md", "moduleName": "multicolumnssample", "abilityName": "MulticolumnssampleAbility", "order": 1}]}, {"id": 15, "categoryType": 2, "order": 1, "sampleDetail": [{"id": 20, "title": "文字特效合集", "desc": "本示例基于Text组件及通用属性实现多种文字特效。帮助开发者在ArkTS页面开发中实现文字渐变、歌词滚动、文字倒影、跑马灯渐变等多种文字效果。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/style/sample_text_effects.gif", "originalUrl": "https://gitee.com/harmonyos_samples/text-effects/blob/br_release_hmosworld_new/README.md", "moduleName": "texteffectssample", "abilityName": "TexteffectssampleAbility", "order": 1}, {"id": 35, "title": "常见Tab导航样式合集", "desc": "Tabs组件可以让用户能聚焦于当前显示的内容，对页面内容进行分类，提高页面空间利用率。本示例基于Tabs组件，为开发者提供不同场景下的导航样式，如：常见底部导航、舵式底部导航、可滑动+更多按钮样式、侧边导航等。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/style/sample_multi_tab_navigation.png", "originalUrl": "https://gitee.com/harmonyos_samples/multi-tab-navigation/blob/br_release_hmosworld_new/README.md", "moduleName": "multitabnavigationsample", "abilityName": "MultitabnavigationsampleAbility", "order": 2}, {"id": 36, "title": "自定义弹窗合集", "desc": "本示例通过CustomDialog、bindContentCover、bindSheet等接口，实现多种样式的弹窗。帮助开发者掌握自定义弹窗开发的步骤，灵活的实现自己业务需要用到的弹窗场景。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/style/sample_custom_dialog_gathers.png", "originalUrl": "https://gitee.com/harmonyos_samples/custom-dialog-gathers/blob/br_release_hmosworld_new/README.md", "moduleName": "customdialogsample", "abilityName": "CustomdialogsampleAbility", "order": 3}]}, {"id": 6, "categoryType": 2, "order": 2, "sampleDetail": [{"id": 48, "title": "Scroll组件嵌套滑动", "desc": "本示例通过Scroll组件的滑动能力和List组件的nestedScroll属性，实现当Scroll嵌套List滑动时，优先滑动最外层的Scroll，当Scroll滑动至末端时，List再继续滚动。帮助开发者掌握Scroll嵌套List滑动时的场景如何处理。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/layout/sample_scroll_component_nested_sliding.png", "originalUrl": "https://gitee.com/harmonyos_samples/scroll-component-nested-sliding/blob/br_release_hmosworld_new/README.md", "moduleName": "nestedslidingsample", "abilityName": "NestedslidingsampleAbility", "order": 1}, {"id": 25, "title": "WaterFlow瀑布流实例", "desc": "本示例为开发者展示使用WaterFlow瀑布流容器实现首页布局效果，包括使用sections实现混排布局、结合item实现滑动吸顶、多种组件混合排列等场景。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/layout/sample_water_flow.png", "originalUrl": "https://gitee.com/harmonyos_samples/water-flow/blob/br_release_hmosworld_new/README.md", "moduleName": "waterflowsample", "abilityName": "WaterflowsampleAbility", "order": 2}, {"id": 43, "title": "组件堆叠", "desc": "本示例介绍运用Stack组件以构建多层次堆叠的视觉效果。通过绑定Scroll组件的onScrollFrameBegin滚动事件回调函数，精准捕获滚动动作的发生。当滚动时，实时地调节组件的透明度、高度等属性，从而成功实现了嵌套滚动效果、透明度动态变化以及平滑的组件切换。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/layout/sample_component_stack.png", "originalUrl": "https://gitee.com/harmonyos_samples/component-stack/blob/br_release_hmosworld_new/README.md", "moduleName": "componentstacksample", "abilityName": "ComponentstacksampleAbility", "order": 3}, {"id": 12, "title": "基于Grid实现混合布局", "desc": "本示例主要实现了Grid组件和List组件以及Swiper组件的嵌套混合布局。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/layout/sample_grid_hybrid.png", "originalUrl": "https://gitee.com/harmonyos_samples/grid-hybrid/blob/br_release_hmosworld_new/README.md", "moduleName": "gridhybridsample", "abilityName": "GridhybridsampleAbility", "order": 4}]}, {"id": 3, "categoryType": 2, "order": 3, "sampleDetail": [{"id": 30, "title": "转场动效合集", "desc": "本示例基于基础组件、通用属性、显式动效，实现多模态页面转场动效以及多种常见一镜到底转场动效，便于用户进行常见的转场动效场景开发。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/effect/sample_transitions_collection.gif", "originalUrl": "https://gitee.com/harmonyos_samples/transitions-collection/blob/br_release_hmosworld_new/README.md", "moduleName": "transitionscollectionsample", "abilityName": "TransitionscollectionsampleAbility", "order": 1}, {"id": 9, "title": "动效案例合集", "desc": "本示例基于基础组件、通用属性、显式动效，实现多种常见动效案例，便于用户进行常见的动效场景开发。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/effect/sample_animation_collection.png", "originalUrl": "https://gitee.com/harmonyos_samples/animation-collection/blob/br_release_hmosworld_new/README.md", "moduleName": "animationcollectionsample", "abilityName": "AnimationcollectionsampleAbility", "order": 2}, {"id": 42, "title": "拖拽框架开发实践", "desc": "本示例基于ArkUI的拖拽框架，实现图片、富文本、文本、输入框、列表等组件的拖拽功能，通过设置拖拽事件中的接口信息自定义拖拽响应，实现拖拽图像增加水印、自定义拖拽背板图、AI识别拖拽内容等拖拽场景。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/effect/sample_drag_framework.png", "originalUrl": "https://gitee.com/harmonyos_samples/DragFramework/blob/br_release_hmosworld_new/README.md", "moduleName": "dragframeworksample", "abilityName": "DragframeworksampleAbility", "order": 3}]}, {"id": 7, "categoryType": 2, "order": 4, "sampleDetail": [{"id": 24, "title": "流畅刷文章", "desc": "本示例实现了文章和媒体文件浏览的功能，通过设置组件的属性来控制屏幕刷新率，达到低功耗的目的，参考本示例可学习开发类似博客场景，并进行低功耗的适配。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/list/sample_fluent_blog.png", "originalUrl": "https://gitee.com/harmonyos_samples/fluent-blog/blob/br_release_hmosworld_new/README.md", "moduleName": "fluentblogsample", "abilityName": "FluentblogsampleAbility", "order": 1}, {"id": 14, "title": "列表项交换", "desc": "本示例介绍了如何通过组合手势结合List组件，来实现对List组件中列表项的交换排序。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/list/sample_list_exchange.png", "originalUrl": "https://gitee.com/harmonyos_samples/list-exchange/blob/br_release_hmosworld_new/README.md", "moduleName": "listexchangesample", "abilityName": "ListexchangesampleAbility", "order": 2}, {"id": 15, "title": "列表编辑效果", "desc": "本示例基于List组件，实现待办事项管理、文件管理、备忘录的等场景列表编辑效果。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/list/sample_list_item_edit.png", "originalUrl": "https://gitee.com/harmonyos_samples/list-item-edit/blob/br_release_hmosworld_new/README.md", "moduleName": "listitemeditsample", "abilityName": "ListitemeditsampleAbility", "order": 3}]}, {"id": 1, "categoryType": 3, "order": 2, "sampleDetail": [{"id": 22, "title": "画中画效果实现", "desc": "本示例基于媒体服务和ArkUI的基本能力，实现视频播放、手动和自动拉起画中画、画中画窗口控制视频播放和暂停等功能。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/media/sample_window_pip.png", "originalUrl": "https://gitee.com/harmonyos_samples/window-pip/blob/br_release_hmosworld_new/README.md", "moduleName": "windowpipsample", "abilityName": "WindowpipsampleAbility", "order": 1}, {"id": 29, "title": "多图片合集", "desc": "本示例介绍了如何使用Swiper组件实现图片轮播效果，以及如何自定义Swiper组件的指示器，来实现图片的切换效果。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/media/sample_multiple_image.png", "originalUrl": "https://gitee.com/harmonyos_samples/MultipleImage/blob/br_release_hmosworld_new/README.md", "moduleName": "multipleimagesample", "abilityName": "MultipleimagesampleAbility", "order": 2}, {"id": 51, "title": "基于AudioRenderer的音频播控和多场景交互", "desc": "本场景解决方案主要面向前台音频开发人员。指导开发者基于AudioRenderer开发音频播控功能。功能包括后台播放、和播控中心的交互、适配不同类型的焦点打断策略、切换路由发声设备、切换输出设备等基础音频常见功能。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/media/sample_audio_interaction.png", "originalUrl": "https://gitee.com/harmonyos_samples/audio-interaction/blob/br_release_hmosworld_new/README.md", "moduleName": "audiointeractionsample", "abilityName": "AudiointeractionsampleAbility", "order": 3}]}, {"id": 34, "categoryType": 3, "order": 3, "sampleDetail": [{"id": 26, "title": "H5页面跳转", "desc": "本示例基于ArkUI框架和Web实现了H5页面和ArkTS页面之间的相互跳转。帮助开发者在Web页面开发中掌握H5页面加载，H5页面跳转，H5页面与ArkTS页面参数传递等功能的实现方案。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/capacity/sample_page_redirection.png", "originalUrl": "https://gitee.com/harmonyos_samples/page-redirection/blob/br_release_hmosworld_new/README.md", "moduleName": "pageredirectionsample", "abilityName": "PageredirectionsampleAbility", "order": 1}, {"id": 31, "title": "Web页面瞬开效果实践", "desc": "本示例基于预渲染技术，实现了点击后Web页面瞬间打开的效果，无需额外加载过程，减少用户等待时长，提高了用户体验。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/capacity/sample_web_pre_render.png", "originalUrl": "https://gitee.com/harmonyos_samples/web-pre-render/blob/br_release_hmosworld_new/README.md", "moduleName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "abilityName": "WebprerendersampleAbility", "order": 2}, {"id": 49, "title": "定位服务", "desc": "本示例通过@kit.LocationKit中的geoLocationManager实现获取缓存位置、获取当前位置，同时运用map.Marker将位置信息标记在地图上。开发者可以在需要用到设备位置信息的开发场景中，如查看所在城市天气、出行打车、旅行导航以及观察运动轨迹等，集成本示例代码实现定位功能。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/capacity/sample_location_service.png", "originalUrl": "https://gitee.com/harmonyos_samples/location-service/blob/br_release_hmosworld_new/README.md", "moduleName": "locationservicesample", "abilityName": "LocationservicesampleAbility", "order": 3}]}, {"id": 33, "categoryType": 3, "order": 5, "sampleDetail": [{"id": 17, "title": "首选项", "desc": "本示例使用@ohos.data.preferences接口，展示了使用首选项持久化存储数据的功能。帮助开发者实现主题切换且主题数据缓存读取的场景。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/data/sample_preferences.png", "originalUrl": "https://gitee.com/harmonyos_samples/preferences/blob/br_release_hmosworld_new/README.md", "moduleName": "preferencessample", "abilityName": "PreferencessampleAbility", "order": 1}, {"id": 21, "title": "验证码场景合集", "desc": "本示例实现了5种验证码场景，基本涵盖了大部分应用的验证码场景。开发者可按需下载代码，实现自己应用的验证码场景。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/scene/sample_verification_code_scenario.png", "originalUrl": "https://gitee.com/harmonyos_samples/verification-code-scenario/blob/br_release_hmosworld_new/README.md", "moduleName": "verificationcodescenariosample", "abilityName": "VerificationcodescenariosampleAbility", "order": 2}, {"id": 13, "title": "发布图片评论", "desc": "本示例通过拉起系统相机实现发布图片评论，便于用户了解系统相机接口的调用方式。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/scene/sample_image_comment.png", "originalUrl": "https://gitee.com/harmonyos_samples/image-comment/blob/br_release_hmosworld_new/README.md", "moduleName": "imagecommentsample", "abilityName": "ImagecommentsampleAbility", "order": 3}, {"id": 50, "title": "选择并查看文档与媒体文件", "desc": "应用使用@ohos.file.picker、@ohos.file.photoAccessHelper、@ohos.file.fs等接口，实现了拉起文档编辑保存、拉起系统相册图片查看、拉起视频并播放的功能。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/function/data/sample_picker.png", "originalUrl": "https://gitee.com/harmonyos_samples/picker/blob/br_release_hmosworld_new/README.md", "moduleName": "pickersample", "abilityName": "PickersampleAbility", "order": 4}, {"id": 47, "title": "UI框架-软键盘弹出", "desc": "本示例展示了输入框分别在屏幕顶部和底部时软键盘弹出对页面布局的影响，通过设置软键盘的避让模式为KeyboardAvoidMode.RESIZE、设置NavDestination的mode为NavDestinationMode.DIALOG等方式实现布局的避让，帮助开发者在多种软件盘弹出场景下实现合理的页面布局。", "preInstalled": false, "sampleType": "commonSample", "isFavorite": false, "mediaType": 1, "mediaUrl": "image/sample/arkui/scene/sample_keyboard.png", "originalUrl": "https://gitee.com/harmonyos_samples/keyboard/blob/br_release_hmosworld_new/README.md", "moduleName": "keyboardsample", "abilityName": "KeyboardsampleAbility", "order": 5}]}]}