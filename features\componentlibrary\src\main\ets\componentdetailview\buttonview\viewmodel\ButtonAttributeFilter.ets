/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import type { ObservedArray } from '@ohos/common';
import type { Attribute } from '../../../viewmodel/Attribute';
import { CommonAttributeFilter } from '../../../viewmodel/CommonAttributeFilter';

export class ButtonAttributeFilter implements CommonAttributeFilter {
  public filter(attributes: ObservedArray<Attribute>): void {
    attributes.forEach((attribute) => {
      switch (attribute.name) {
        case 'buttonStyle':
          const buttonTypeIndex = attributes.findIndex((item) => item.name === 'buttonType');
          const operationIndex = attributes.findIndex((item) => item.name === 'operation');
          const backgroundColorIndex = attributes.findIndex((item) => item.name === 'backgroundColor');
          if (buttonTypeIndex === -1 || operationIndex === -1 || backgroundColorIndex === -1) {
            return;
          }
          if (attribute.currentValue === 'Textual') {
            attributes[buttonTypeIndex].enable = false;
            attributes[operationIndex].enable = true;
            attributes[backgroundColorIndex].enable = false;
          } else if (attribute.currentValue === 'Emphasized') {
            attributes[buttonTypeIndex].enable = true;
            attributes[operationIndex].enable = true;
            attributes[backgroundColorIndex].enable = true;
          } else {
            attributes[buttonTypeIndex].enable = true;
            attributes[operationIndex].enable = true;
            attributes[backgroundColorIndex].enable = false;
          }
          break;
        default:
          break;
      }
    });
  }
}